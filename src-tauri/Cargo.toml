[package]
name = "ATM"
version = "0.2.2"
description = "Augment Token Manager - 用于生成和管理 Augment OAuth 访问令牌的桌面应用"
authors = ["cubezhao"]
license = "MIT"
repository = "https://github.com/zhaochengcube/augment-token-mng"
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[build-dependencies]
tauri-build = { version = "2.0", features = [] }

[dependencies]
tauri = { version = "2.0", features = [] }
tauri-plugin-shell = "2"
tauri-plugin-opener = "2"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
reqwest = { version = "0.11", features = ["json"] }
tokio = { version = "1.0", features = ["full"] }
warp = "0.3"
open = "5.0"
base64 = "0.21"
sha2 = "0.10"
rand = "0.8"
url = "2.4"
uuid = { version = "1.0", features = ["v4", "serde"] }
chrono = { version = "0.4", features = ["serde"] }
# 邮件功能依赖
imap = "2.4"
native-tls = "0.2"

[features]
# this feature is used for production builds or when `devPath` points to the filesystem
# DO NOT REMOVE!!
custom-protocol = ["tauri/custom-protocol"]
